from deepeval.test_case import LLMTestCase, LLMTestCaseParams
from deepeval import assert_test
from deepeval.metrics import GEval
from openai import OpenAI

# Define a simple accuracy metric
accuracy_metric = GEval(
    name="Semantic Consistency",
    criteria="Evaluate how semantically similar the second model response is to the first one, even if worded differently.",
    evaluation_params=[
        LLMTestCaseParams.ACTUAL_OUTPUT,
        LLMTestCaseParams.EXPECTED_OUTPUT
    ],
    threshold=0.7
)

def test_simple():
    
    test_case = LLMTestCase(
        input="Why is the sky blue?",

        actual_output="The capital of France is Paris",
        expected_output="The first response",
        context=["Light scattering and atmospheric physics"]
    )
    # This is required - assert_test() actually runs the evaluation
    assert_test(test_case, [accuracy_metric])

# Optional: Allow running as a standalone script
if __name__ == "__main__":
    print("Running simple test...")
    test_simple()
    print("Test completed!")
