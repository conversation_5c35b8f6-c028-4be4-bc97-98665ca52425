from deepeval.test_case import LLMTestCase, LLMTestCaseParams
from deepeval import assert_test
from deepeval.metrics import GEval

# Define a simple accuracy metric
accuracy_metric = GEval(
    name="Accuracy",
    criteria="Determine whether the actual output is factually correct based on the expected output.",
    evaluation_params=[
        LLMTestCaseParams.ACTUAL_OUTPUT,
        LLMTestCaseParams.EXPECTED_OUTPUT
    ],
    threshold=0.7
)

def test_simple():
    """
    Simple test function that DeepEval CLI can find and run.
    Function name MUST start with 'test_' for DeepEval to recognize it.
    """
    test_case = LLMTestCase(
        input="What is the capital of France?",
        actual_output="The capital of France is Paris",
        expected_output="Paris",
        context=["France is a country in Europe"]
    )
    # This is required - assert_test() actually runs the evaluation
    assert_test(test_case, [accuracy_metric])

# Optional: Allow running as a standalone script
if __name__ == "__main__":
    print("Running simple test...")
    test_simple()
    print("Test completed!")
