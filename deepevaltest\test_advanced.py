from deepeval import evaluate
from deepeval.test_case import LLMTestCase
from deepeval.metrics import AnswerRelevancyMetric
from openai import OpenAI
import os

# Initialize your OpenAI client
client = OpenAI(api_key=os.getenv("OPENAI_API_KEY"))

def test_advanced():
    # Step 1: Prepare your input
    question = "What is the capital of France?"
    
    # Step 2: Get LLM response
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": question}]
    )
    answer = response.choices[0].message.content
    
    # Step 3: Create test case
    test_case = LLMTestCase(
        input=question,
        actual_output=answer
    )
    
    # Step 4: Choose metrics
    relevancy_metric = AnswerRelevancyMetric(threshold=0.7)
    
    # Step 5: Run evaluation
    result = evaluate([test_case], [relevancy_metric])
    
    # Check if test passed
    if result[0].success:
        print(" Test passed!")
    else:
        print(" Test failed!")
    
    print(f"Score: {result[0].metrics_metadata[0].score}")
    print(f"Reason: {result[0].metrics_metadata[0].reason}")

# Run the test
test_advanced()
