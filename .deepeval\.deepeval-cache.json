{"test_cases_lookup_map": {"{\"actual_output\": \"The capital of France is Paris\", \"context\": [\"France is a country in Europe\"], \"expected_output\": \"Paris\", \"hyperparameters\": null, \"input\": \"What is the capital of France?\", \"retrieval_context\": null}": {"cached_metrics_data": [{"metric_data": {"name": "Accuracy [GEval]", "threshold": 0.7, "success": true, "score": 0.8090205881110007, "reason": "The actual output is factually consistent with the expected output, correctly identifying Paris as the capital of France. However, it includes additional context not present in the expected output, which only required the single word 'Paris'. All key facts are represented, but the extra information slightly diverges from the expected format.", "strictMode": false, "evaluationModel": "gpt-4.1", "evaluationCost": 0, "verboseLogs": "Criteria:\nDetermine whether the actual output is factually correct based on the expected output. \n \nEvaluation Steps:\n[\n    \"Compare the actual output to the expected output for factual consistency.\",\n    \"Identify any factual inaccuracies or discrepancies in the actual output when compared to the expected output.\",\n    \"Determine if all key facts present in the expected output are correctly represented in the actual output.\",\n    \"Conclude whether the actual output is factually correct based on the comparison.\"\n] \n \nRubric:\nNone \n \nScore: 0.8090205881110007"}, "metric_configuration": {"threshold": 0.7, "evaluation_model": "gpt-4.1", "strict_mode": false, "criteria": "Determine whether the actual output is factually correct based on the expected output.", "include_reason": false, "evaluation_steps": ["Compare the actual output to the expected output for factual consistency.", "Identify any factual inaccuracies or discrepancies in the actual output when compared to the expected output.", "Determine if all key facts present in the expected output are correctly represented in the actual output.", "Conclude whether the actual output is factually correct based on the comparison."], "evaluation_params": ["actual_output", "expected_output"]}}]}}}