from deepeval.test_case import LLMTestCase, LLMTestCaseParams
from deepeval import assert_test
from deepeval.metrics import HallucinationMetric
from deepeval.metrics import GEval
from deepeval import evaluate    
from openai import OpenAI


def test_hallucination():
    
    test_case = LLMTestCase(
        input="Who discovered penicillin and what year was it?",

        actual_output="Penicillin was discovered by <PERSON> in 1928.
                        He noticed that a mold called Penicillium notatum had killed bacteria in a petri dish he had accidentally left uncovered. This observation led to the development of penicillin, the world's first widely used antibiotic.
                    The large-scale production of penicillin didn’t begin until the early 1940s, during World War II, with key contributions from scientists like <PERSON>, <PERSON>, and <PERSON>.  ",
        context=["<PERSON>, 1928"]
    )
    # This is required - assert_test() actually runs the evaluation
    assert_test(test_case, [HallucinationMetric])

    HallucinationMetric = HallucinationMetric(threshold=0.7)
    result = evaluate([test_case], [HallucinationMetric])

# Optional: Allow running as a standalone script
if __name__ == "__main__":
    print("Running faithfulness test...")
    test_hallucination()
    print("Test completed!")
