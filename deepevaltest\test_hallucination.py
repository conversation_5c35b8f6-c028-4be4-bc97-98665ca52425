from deepeval.test_case import LLMTestCase
from deepeval import assert_test
from deepeval.metrics import HallucinationMetric


def test_hallucination():
    #Create Test Case with Input, Output, and Context
    test_case = LLMTestCase(
        input="Who discovered penicillin and what year was it?",
        actual_output="Penicillin was discovered by <PERSON> in 1928. He noticed that a mold called Penicillium notatum had killed bacteria in a petri dish he had accidentally left uncovered. This observation led to the development of penicillin, the world's first widely used antibiotic. The large-scale production of penicillin didn’t begin until the early 1940s, during World War II, with key contributions from scientists like <PERSON>, <PERSON>, and <PERSON>.",
        context=["<PERSON> discovered penicillin in 1928"]
    )

    #Create Hallucination Metric
    hallucination_metric = HallucinationMetric(threshold=0.7)

    #Runs the test
    assert_test(test_case, [hallucination_metric])



# Optional: Allow running as a standalone script
if __name__ == "__main__":
    print("Running hallucination test...")
    test_hallucination()
    print("Test completed!")
